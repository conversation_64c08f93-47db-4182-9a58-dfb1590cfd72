/* Sayonika Store Styles */

.sayonika-store {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

/* Header Section */
.store-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #333;
    flex-shrink: 0;
}

/* Scrollable Content Area */
.store-content {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
    padding-right: 5px; /* Add some space for scrollbar */
    padding-bottom: 20px; /* Add bottom padding for better scrolling */
}

/* Custom scrollbar for store content */
.store-content::-webkit-scrollbar {
    width: 8px;
}

.store-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.store-content::-webkit-scrollbar-thumb {
    background: rgba(255, 105, 180, 0.6);
    border-radius: 4px;
    transition: background 0.2s ease;
}

.store-content::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 105, 180, 0.8);
}

.store-title h1 {
    margin: 0;
    color: #ff69b4;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.store-title p {
    margin: 5px 0 0 0;
    color: #ccc;
    font-size: 1.1em;
}

.store-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* Search Container */
.search-container {
    position: relative;
}

.search-input {
    padding: 10px 40px 10px 15px;
    border: 2px solid #444;
    border-radius: 25px;
    background: #222;
    color: #fff;
    font-size: 14px;
    width: 250px;
    transition: border-color 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #ff69b4;
}

.search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
}

/* Authentication Section */
.auth-section {
    display: flex;
    align-items: center;
}



.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    background: #333;
    padding: 8px 15px;
    border-radius: 20px;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid #ff69b4;
    object-fit: cover;
    background: #333;
    display: block;
}

.username {
    color: #fff;
    font-weight: bold;
}

.btn-logout {
    background: #666;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 15px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.btn-logout:hover {
    background: #888;
}

/* Filters Section */
.store-filters {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    padding: 15px;
    background: #222;
    border-radius: 10px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-group label {
    color: #ccc;
    font-weight: bold;
}

.filter-group select {
    padding: 8px 12px;
    border: 1px solid #444;
    border-radius: 5px;
    background: #333;
    color: #fff;
    cursor: pointer;
}

.filter-group input[type="checkbox"] {
    margin-right: 5px;
}

/* Loading and Error States */
.loading-container, .error-container {
    text-align: center;
    padding: 60px 20px;
    color: #ccc;
}

.loading-container i {
    font-size: 3em;
    color: #ff69b4;
    margin-bottom: 20px;
}

.error-container i {
    font-size: 3em;
    color: #ff4444;
    margin-bottom: 20px;
}

.btn-retry {
    background: #ff69b4;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 15px;
}

/* Mods Grid */
.mods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.no-mods {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: #888;
}

.no-mods i {
    font-size: 3em;
    margin-bottom: 20px;
}

/* Mod Cards */
.mod-card {
    background: #222;
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.mod-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(255, 105, 180, 0.3);
    border-color: #ff69b4;
}

.mod-thumbnail {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.mod-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.mod-card:hover .mod-thumbnail img {
    transform: scale(1.05);
}

.featured-badge, .nsfw-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.featured-badge {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
}

.nsfw-badge {
    background: linear-gradient(45deg, #ff4444, #cc0000);
    color: white;
}

.mod-info {
    padding: 15px;
}

.mod-title {
    margin: 0 0 8px 0;
    color: #fff;
    font-size: 1.2em;
    font-weight: bold;
}

.mod-author {
    margin: 0 0 10px 0;
    color: #ff69b4;
    font-size: 0.9em;
}

.mod-description {
    margin: 0 0 15px 0;
    color: #ccc;
    font-size: 0.9em;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.mod-stats {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
    font-size: 0.8em;
    color: #888;
}

.mod-stats span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.mod-tags {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.tag {
    background: #444;
    color: #ccc;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
}

.mod-actions {
    padding: 0 15px 15px 15px;
}

.btn-download {
    width: 100%;
    background: linear-gradient(45deg, #ff69b4, #ff1493);
    color: white;
    border: none;
    padding: 10px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: opacity 0.2s ease;
}

.btn-download:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-download:not(:disabled):hover {
    background: linear-gradient(45deg, #ff1493, #ff69b4);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 30px;
}

.pagination button {
    background: #333;
    color: #fff;
    border: 1px solid #555;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.pagination button:not(:disabled):hover {
    background: #555;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-info {
    color: #ccc;
    font-weight: bold;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: #222;
    border-radius: 10px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    border: 2px solid #444;
}

.mod-details-modal {
    max-width: 800px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #444;
}

.modal-header h2 {
    margin: 0;
    color: #fff;
}

.modal-close {
    background: none;
    border: none;
    color: #888;
    font-size: 1.5em;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.2s ease;
}

.modal-close:hover {
    background: #444;
    color: #fff;
}

.modal-body {
    padding: 20px;
}

/* Login Modal */
.login-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.btn-oauth {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 12px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: transform 0.2s ease;
}

.btn-oauth:hover {
    transform: translateY(-2px);
}

.btn-oauth.discord {
    background: #5865f2;
    color: white;
}

.btn-oauth.github {
    background: #333;
    color: white;
}

.divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
    color: #888;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #444;
}

.divider span {
    background: #222;
    padding: 0 15px;
    position: relative;
}

.credential-login {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.form-group label {
    color: #ccc;
    font-weight: bold;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"] {
    padding: 10px;
    border: 1px solid #444;
    border-radius: 5px;
    background: #333;
    color: #fff;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input[type="text"]:focus,
.form-group input[type="email"]:focus,
.form-group input[type="password"]:focus {
    outline: none;
    border-color: #ff69b4;
    box-shadow: 0 0 5px rgba(255, 105, 180, 0.3);
}

.input-help {
    color: #888;
    font-size: 12px;
    margin-top: 4px;
    font-style: italic;
}

.btn-submit {
    background: linear-gradient(45deg, #ff69b4, #ff1493);
    color: white;
    border: none;
    padding: 12px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: opacity 0.2s ease;
}

.btn-submit:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.register-link {
    text-align: center;
    margin-top: 15px;
}

.register-link a {
    color: #ff69b4;
    text-decoration: none;
}

.register-link a:hover {
    text-decoration: underline;
}

/* Mod Details Modal */
.mod-details-content {
    display: flex;
    gap: 30px;
}

.mod-details-left {
    flex: 0 0 300px;
}

.mod-details-right {
    flex: 1;
}

.mod-details-image {
    width: 100%;
    border-radius: 10px;
    margin-bottom: 20px;
}

.mod-details-stats {
    background: #333;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.stat {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    color: #ccc;
}

.stat:last-child {
    margin-bottom: 0;
}

.stat i {
    color: #ff69b4;
    width: 16px;
}

.btn-download-large {
    width: 100%;
    background: linear-gradient(45deg, #ff69b4, #ff1493);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 10px;
    cursor: pointer;
    font-weight: bold;
    font-size: 1.1em;
    transition: opacity 0.2s ease;
}

.btn-download-large:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.mod-description h3,
.mod-tags-section h3,
.mod-requirements h3 {
    color: #fff;
    margin-bottom: 10px;
    border-bottom: 1px solid #444;
    padding-bottom: 5px;
}

.mod-description div {
    color: #ccc;
    line-height: 1.6;
}

.tags-list {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.mod-requirements ul {
    color: #ccc;
    margin: 0;
    padding-left: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .store-header {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
    }

    .store-actions {
        flex-direction: column;
        gap: 15px;
    }

    .search-input {
        width: 100%;
    }

    .store-filters {
        flex-direction: column;
        gap: 15px;
    }

    .mods-grid {
        grid-template-columns: 1fr;
    }

    .mod-details-content {
        flex-direction: column;
    }

    .mod-details-left {
        flex: none;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }
}
